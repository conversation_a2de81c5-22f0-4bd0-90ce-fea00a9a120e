#!/usr/bin/env python3
"""
Test script for the updated InternVL3 inference
Tests the new functionality without running full inference
"""

import json
import torch

def test_prompt_loading():
    """Test loading prompts from final_prompt.jsonl"""
    print("🧪 Testing Prompt Loading")
    print("=" * 40)
    
    # Import the function from our script
    import sys
    sys.path.append('.')
    from internvl3_final_prompt_inference import load_prompts_from_jsonl, remove_urls_from_text
    
    # Load prompts
    prompts = load_prompts_from_jsonl('final_prompt.jsonl')
    
    if prompts:
        print(f"✅ Loaded {len(prompts)} prompts")
        
        # Test first prompt
        first_prompt = prompts[0]
        print(f"\n🔍 First prompt structure:")
        print(f"  Keys: {list(first_prompt.keys())}")
        
        prompt_text = first_prompt.get('prompt', '')
        response_text = first_prompt.get('response', '')
        image_urls = first_prompt.get('images', [])
        
        print(f"  Prompt length: {len(prompt_text):,} chars")
        print(f"  Response length: {len(response_text):,} chars")
        print(f"  Image URLs: {len(image_urls)}")
        
        # Test URL removal
        print(f"\n🔗 Testing URL removal:")
        cleaned_prompt, urls_removed = remove_urls_from_text(prompt_text)
        chars_saved = len(prompt_text) - len(cleaned_prompt)
        
        print(f"  Original: {len(prompt_text):,} chars")
        print(f"  Cleaned: {len(cleaned_prompt):,} chars")
        print(f"  URLs removed: {urls_removed}")
        print(f"  Chars saved: {chars_saved:,}")
        
        # Show first few image URLs
        print(f"\n🖼️ First few image URLs:")
        for i, url in enumerate(image_urls[:3]):
            print(f"  {i+1}: {url}")
        
        return True
    else:
        print("❌ Failed to load prompts")
        return False

def test_image_preprocessing():
    """Test the InternVL3 image preprocessing functions"""
    print("\n🧪 Testing Image Preprocessing Functions")
    print("=" * 40)
    
    try:
        from internvl3_final_prompt_inference import (
            build_transform, 
            dynamic_preprocess, 
            find_closest_aspect_ratio,
            IMAGENET_MEAN,
            IMAGENET_STD
        )
        
        print("✅ Successfully imported preprocessing functions")
        print(f"  IMAGENET_MEAN: {IMAGENET_MEAN}")
        print(f"  IMAGENET_STD: {IMAGENET_STD}")
        
        # Test transform building
        transform = build_transform(input_size=448)
        print(f"✅ Built transform for input_size=448")
        
        # Test aspect ratio finding
        target_ratios = [(1, 1), (1, 2), (2, 1), (2, 2)]
        best_ratio = find_closest_aspect_ratio(1.5, target_ratios, 600, 400, 448)
        print(f"✅ Found closest aspect ratio for 1.5: {best_ratio}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing preprocessing: {e}")
        return False

def test_gpu_availability():
    """Test GPU availability for InternVL3"""
    print("\n🧪 Testing GPU Availability")
    print("=" * 40)
    
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        current_device = torch.cuda.current_device()
        gpu_name = torch.cuda.get_device_name(current_device)
        gpu_memory = torch.cuda.get_device_properties(current_device).total_memory / 1024**3
        
        print(f"✅ CUDA available")
        print(f"  GPU: {gpu_name}")
        print(f"  Memory: {gpu_memory:.1f} GB")
        print(f"  Device count: {gpu_count}")
        
        if gpu_memory >= 16:
            print(f"✅ Sufficient memory for InternVL3-8B")
        else:
            print(f"⚠️ Limited memory - may need optimization")
        
        return True
    else:
        print("❌ CUDA not available")
        return False

def test_json_cleaning():
    """Test JSON response cleaning"""
    print("\n🧪 Testing JSON Response Cleaning")
    print("=" * 40)
    
    try:
        from internvl3_final_prompt_inference import clean_json_response
        
        test_cases = [
            '```json\n{"test": "value", "number": 123}\n```',
            '{"direct": "json", "works": true}',
            'Some text\n```json\n{"embedded": "json"}\n```\nMore text',
            '{"complex": {"nested": {"data": [1, 2, 3]}}}',
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            cleaned = clean_json_response(test_case)
            print(f"  Test {i}: {test_case[:30]}...")
            print(f"    Cleaned: {cleaned}")
            
            try:
                parsed = json.loads(cleaned)
                print(f"    ✅ Valid JSON")
            except:
                print(f"    ❌ Invalid JSON")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing JSON cleaning: {e}")
        return False

def test_file_structure():
    """Test that all required files exist"""
    print("\n🧪 Testing File Structure")
    print("=" * 40)
    
    import os
    
    required_files = [
        'final_prompt.jsonl',
        'internvl3_final_prompt_inference.py',
        'requirements_internvl3.txt'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path) / 1024 / 1024  # MB
            print(f"  ✅ {file_path} ({size:.1f} MB)")
        else:
            print(f"  ❌ {file_path} (missing)")
            all_exist = False
    
    return all_exist

def main():
    """Run all tests"""
    print("🔬 InternVL3 Updated Script Tests")
    print("=" * 50)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Prompt Loading", test_prompt_loading),
        ("Image Preprocessing", test_image_preprocessing),
        ("GPU Availability", test_gpu_availability),
        ("JSON Cleaning", test_json_cleaning),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Script is ready for inference.")
    else:
        print("⚠️ Some tests failed. Check the issues above.")

if __name__ == "__main__":
    main()
