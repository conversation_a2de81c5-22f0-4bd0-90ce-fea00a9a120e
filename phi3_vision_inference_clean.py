#!/usr/bin/env python3
"""
Clean Phi-3-Vision Inference
No truncation, no similarity scoring - pure inference with URL removal
Temperature: 0.3, Clean architecture with utils
"""

import json
import time
import re
from datetime import datetime
from vllm import SamplingParams

# Import utilities from utils folder
from utils.data_loader import load_prompts_from_jsonl
from utils.image_processor import prepare_image_conservative
from utils.text_processor import remove_urls_from_text, estimate_tokens
from utils.model_initializer import initialize_phi3_with_fallback

def clean_json_response(response_text: str) -> str:
    """
    Clean the generated response to extract valid JSON format.
    Removes markdown formatting and extracts the JSON content.
    """
    if not response_text:
        return ""

    # Remove markdown code blocks
    response_text = re.sub(r'```json\s*', '', response_text)
    response_text = re.sub(r'```\s*$', '', response_text)
    response_text = re.sub(r'^```\s*', '', response_text)

    # Find JSON content between braces
    # Look for the first { and try to find the matching }
    start_idx = response_text.find('{')
    if start_idx == -1:
        return response_text.strip()

    # Count braces to find the end of JSON
    brace_count = 0
    end_idx = start_idx

    for i, char in enumerate(response_text[start_idx:], start_idx):
        if char == '{':
            brace_count += 1
        elif char == '}':
            brace_count -= 1
            if brace_count == 0:
                end_idx = i + 1
                break

    # Extract the JSON portion
    json_content = response_text[start_idx:end_idx].strip()

    # Validate that it's proper JSON
    try:
        json.loads(json_content)
        return json_content
    except json.JSONDecodeError:
        # If it's not valid JSON, return the cleaned text anyway
        return json_content

def main():
    """Clean Phi-3-Vision inference with URL removal"""
    print("🧹 Clean Phi-3-Vision Inference")
    print("=" * 50)
    print("🎯 Features: URL removal, No truncation, Temperature 0.3")

    # Load prompts
    input_files = ['final_image_prompts_cleaned.jsonl', 'scrape_content_prompts.jsonl']
    prompts = None

    for input_file in input_files:
        prompts = load_prompts_from_jsonl(input_file)
        if prompts:
            print(f"✅ Using prompts from: {input_file}")
            break

    if not prompts:
        print("❌ No prompts found")
        return

    # Initialize model
    print(f"\n🤖 INITIALIZING PHI-3-VISION")
    print("-" * 50)

    try:
        llm, max_context = initialize_phi3_with_fallback()
        print(f"✅ Model ready with {max_context:,} token context")
    except Exception as e:
        print(f"❌ Model initialization failed: {e}")
        return

    # Process prompts
    results = []
    max_prompts = min(len(prompts), 5)  # Process up to 5 prompts

    print(f"\n🚀 PROCESSING {max_prompts} PROMPTS")
    print("=" * 50)

    for i, prompt_data in enumerate(prompts[:max_prompts], 1):
        print(f"\n{'='*15} PROMPT {i}/{max_prompts} {'='*15}")

        try:
            # Extract data
            system_prompt = prompt_data.get('SystemPrompt', '')
            user_prompt = prompt_data.get('UserPrompt', '')
            image_data_urls = prompt_data.get('Images', [])
            actual_response = prompt_data.get('Response', '')

            print(f"📋 Original lengths:")
            print(f"  System: {len(system_prompt):,} chars")
            print(f"  User: {len(user_prompt):,} chars")
            print(f"  Images: {len(image_data_urls)}")
            print(f"  Actual response: {len(actual_response):,} chars")

            # Remove URLs to maximize context efficiency
            print("🔗 Removing URLs...")
            cleaned_system, system_urls = remove_urls_from_text(system_prompt)
            cleaned_user, user_urls = remove_urls_from_text(user_prompt)

            total_urls = system_urls + user_urls
            total_saved = (len(system_prompt) - len(cleaned_system)) + (len(user_prompt) - len(cleaned_user))

            print(f"📝 URL removal results:")
            print(f"  URLs removed: {total_urls}")
            print(f"  Characters saved: {total_saved:,}")
            print(f"  Final system: {len(cleaned_system):,} chars")
            print(f"  Final user: {len(cleaned_user):,} chars")

            # Process images (limit to 2 for memory efficiency)
            processed_images = []
            print(f"🖼️ Processing images...")

            for j, img_data in enumerate(image_data_urls[:2]):
                image = prepare_image_conservative(img_data)
                if image:
                    processed_images.append(image)
                    print(f"  ✓ Image {j+1}: {image.size}")
                else:
                    print(f"  ✗ Image {j+1}: failed")

            if not processed_images:
                print("❌ No valid images, skipping prompt")
                continue

            # Estimate tokens before creating prompt
            combined_text = cleaned_system + cleaned_user
            token_estimate = estimate_tokens(combined_text, len(processed_images))

            print(f"📊 Token estimation:")
            print(f"  Text: {token_estimate['text_tokens']:,} tokens")
            print(f"  Images: {token_estimate['image_tokens']:,} tokens")
            print(f"  Total: {token_estimate['total_tokens']:,} tokens")
            print(f"  Context available: {max_context:,} tokens")

            if token_estimate['total_tokens'] > max_context:
                print(f"⚠️ Warning: Estimated tokens ({token_estimate['total_tokens']:,}) exceed context ({max_context:,})")
                print("🔄 Continuing anyway - vLLM will handle gracefully")
            else:
                print(f"✅ Estimated tokens fit in context")

            # Create Phi-3-Vision prompt (NO TRUNCATION)
            image_tokens = "".join([f"<|image_{i+1}|>" for i in range(len(processed_images))])
            full_prompt = f"<|user|>\n{image_tokens}\n{cleaned_system}\n\n{cleaned_user}<|end|>\n<|assistant|>\n"

            print(f"📏 Final prompt: {len(full_prompt):,} chars (NO TRUNCATION)")

            # Sampling parameters with temperature 0.3
            sampling_params = SamplingParams(
                temperature=0.3,  # Set to 0.3 as requested
                top_p=0.9,
                max_tokens=2000,  # Reasonable output length
                repetition_penalty=1.05,
                stop=["<|end|>", "<|user|>", "<|assistant|>"]
            )

            # Run inference
            print("🚀 Running inference...")
            start_time = time.time()

            outputs = llm.generate(
                [{
                    "prompt": full_prompt,
                    "multi_modal_data": {"image": processed_images}
                }],
                sampling_params=sampling_params
            )

            inference_time = time.time() - start_time

            if outputs and outputs[0].outputs:
                generated_text = outputs[0].outputs[0].text.strip()

                print(f"✅ Inference completed in {inference_time:.2f}s")
                print(f"📝 Generated: {len(generated_text):,} chars")
                print(f"📄 Preview: {generated_text[:200]}...")

                # Clean the generated response to extract JSON
                cleaned_generated_response = clean_json_response(generated_text)

                print(f"🧹 Cleaned response: {len(cleaned_generated_response):,} chars")

                # Store results (simple structure)
                result = {
                    'prompt_id': f"prompt_{i}",
                    'context_used': max_context,
                    'original_system_length': len(system_prompt),
                    'original_user_length': len(user_prompt),
                    'cleaned_system_length': len(cleaned_system),
                    'cleaned_user_length': len(cleaned_user),
                    'urls_removed': total_urls,
                    'chars_saved': total_saved,
                    'num_images': len(processed_images),
                    'actual_response': actual_response,
                    'actual_response_length': len(actual_response),
                    'generated_response_raw': generated_text,
                    'generated_response': cleaned_generated_response,
                    'generated_length': len(cleaned_generated_response),
                    'inference_time': inference_time,
                    'temperature': 0.3,
                    'success': True
                }

                results.append(result)

            else:
                print("❌ No output generated")
                results.append({
                    'prompt_id': f"prompt_{i}",
                    'actual_response': actual_response,
                    'actual_response_length': len(actual_response),
                    'success': False,
                    'error': 'No output generated'
                })

        except Exception as e:
            print(f"❌ Error processing prompt {i}: {e}")
            # Try to get actual_response even if there's an error
            try:
                actual_response = prompt_data.get('Response', '')
            except:
                actual_response = ''

            results.append({
                'prompt_id': f"prompt_{i}",
                'actual_response': actual_response,
                'actual_response_length': len(actual_response),
                'success': False,
                'error': str(e)
            })

    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f'phi3_clean_results_{timestamp}.json'

    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    # Simple summary
    successful = [r for r in results if r.get('success', False)]

    print(f"\n📊 SUMMARY:")
    print("=" * 50)
    print(f"🖥️ Context: {max_context:,} tokens")
    print(f"📊 Processed: {len(results)} prompts")
    print(f"✅ Successful: {len(successful)}")
    print(f"🌡️ Temperature: 0.3")

    if successful:
        avg_time = sum(r['inference_time'] for r in successful) / len(successful)
        total_urls = sum(r['urls_removed'] for r in successful)
        total_saved = sum(r['chars_saved'] for r in successful)
        avg_generated_length = sum(r['generated_length'] for r in successful) / len(successful)
        avg_actual_length = sum(r['actual_response_length'] for r in successful) / len(successful)

        print(f"⏱️ Average time: {avg_time:.2f}s")
        print(f"🔗 Total URLs removed: {total_urls:,}")
        print(f"💾 Total chars saved: {total_saved:,}")
        print(f"📝 Average generated response (cleaned): {avg_generated_length:,.0f} chars")
        print(f"📄 Average actual response: {avg_actual_length:,.0f} chars")

    print(f"\n💾 Results saved to: {results_file}")
    print("🎉 Clean inference completed!")

if __name__ == "__main__":
    main()
