# InternVL3 Final Prompt Inference Guide

## 🎯 Overview

This guide covers the updated `internvl3_final_prompt_inference.py` script that processes your `final_prompt.jsonl` file using the official InternVL3-8B implementation patterns.

## 🔧 Key Updates

### ✅ **Official InternVL3 Implementation**
- Uses exact preprocessing from official InternVL3 examples
- Implements `dynamic_preprocess()` with proper aspect ratio handling
- Includes `build_transform()` with IMAGENET normalization
- Uses official `model.chat()` method for inference

### ✅ **Direct Prompt Format Support**
- Handles `prompt`, `response`, `images` fields from `final_prompt.jsonl`
- No more SystemPrompt/UserPrompt splitting
- Direct prompt processing as you requested

### ✅ **URL Download & Processing**
- Downloads images from URLs (not base64 data)
- Applies official InternVL3 dynamic preprocessing
- Handles multi-image scenarios with `num_patches_list`

### ✅ **Enhanced Output Format**
- **`baseline_TIMESTAMP.json`**: Actual responses from JSONL
- **`generated_TIMESTAMP.json`**: Model-generated responses
- **`input_prompts_TIMESTAMP.json`**: Input prompts with metadata

## 📊 Your Data Analysis

Based on testing with your `final_prompt.jsonl`:

```
📈 Dataset Statistics:
- Total records: 206
- Average prompt: 167,350 chars → 102,449 chars (after URL removal)
- Average response: 19,340 chars
- Average images: 4.1 per record
- URLs removed per prompt: ~331
- Characters saved: ~40,560 per prompt
```

## 🚀 Usage

### **1. Install Dependencies**
```bash
pip install -r requirements_internvl3.txt
```

### **2. Run Inference**
```bash
python internvl3_final_prompt_inference.py
```

### **3. Configuration**
Edit line 444 to change number of prompts:
```python
max_prompts = min(len(prompts), 2)  # Change 2 to desired number
```

## 📁 Output Files

### **baseline.json**
```json
[
  {
    "prompt_id": "prompt_1",
    "response": "actual response from JSONL",
    "response_length": 15734
  }
]
```

### **generated.json**
```json
[
  {
    "prompt_id": "prompt_1",
    "response": "clean JSON from InternVL3",
    "response_length": 2847,
    "inference_time": 22.4,
    "num_images": 4,
    "urls_removed": 331,
    "chars_saved": 40560,
    "original_prompt_length": 143009,
    "cleaned_prompt_length": 102449,
    "model": "InternVL3-8B",
    "temperature": 0.3,
    "success": true
  }
]
```

### **input_prompts.json**
```json
[
  {
    "prompt_id": "prompt_1",
    "original_prompt": "full original prompt",
    "cleaned_prompt": "prompt with URLs removed",
    "original_prompt_length": 143009,
    "cleaned_prompt_length": 102449,
    "urls_removed": 331,
    "chars_saved": 40560,
    "num_images": 6,
    "image_urls": ["https://...", "https://..."]
  }
]
```

## 🔧 Technical Implementation

### **Image Processing Pipeline**
1. **Download**: `load_image_from_url()` downloads from URLs
2. **Preprocess**: `dynamic_preprocess()` splits into optimal tiles
3. **Transform**: `build_transform()` applies IMAGENET normalization
4. **Combine**: Multiple images concatenated with `num_patches_list`

### **Inference Pipeline**
1. **URL Removal**: Reduces prompt size by ~28% (40k chars saved)
2. **Image Processing**: Up to 4 images per prompt, 6 tiles max per image
3. **Prompt Format**: `<image>\nprompt` or `Image-1: <image>\nImage-2: <image>\nprompt`
4. **Generation**: Official `model.chat()` with temperature 0.3

### **Memory Optimization**
- 8-bit quantization (`load_in_8bit=True`)
- Flash attention support (`use_flash_attn=True`)
- Dynamic image tiling (max 6 tiles per image)
- URL removal saves ~40k characters per prompt

## 🎛️ Model Configuration

```python
# Model initialization
model = AutoModel.from_pretrained(
    "OpenGVLab/InternVL3-8B",
    torch_dtype=torch.bfloat16,
    load_in_8bit=True,
    low_cpu_mem_usage=True,
    use_flash_attn=True,
    trust_remote_code=True
).eval()

# Generation config
generation_config = {
    'max_new_tokens': 5000,
    'temperature': 0.3,
    'do_sample': True,
    'top_p': 0.9,
    'repetition_penalty': 1.05
}
```

## 🧪 Testing

Run the test suite to verify everything works:
```bash
python test_internvl3_updated.py
```

Expected output:
```
🎯 Overall: 4/5 tests passed
✅ File Structure: PASS
✅ Prompt Loading: PASS  
✅ Image Preprocessing: PASS
❌ GPU Availability: FAIL (expected without GPU)
✅ JSON Cleaning: PASS
```

## 💾 Hardware Requirements

- **GPU**: 16GB+ VRAM recommended (A40, A100, RTX 4090)
- **RAM**: 32GB+ system RAM
- **Storage**: 50GB+ for model weights
- **CUDA**: Compatible drivers for flash attention

## 🔍 Key Features

### ✅ **Robust Error Handling**
- Continues processing if individual images fail
- Saves partial results even on errors
- Comprehensive logging and progress tracking

### ✅ **Official InternVL3 Patterns**
- Exact implementation from your working examples
- Proper multi-image handling with `num_patches_list`
- Dynamic preprocessing with aspect ratio optimization

### ✅ **Comprehensive Metrics**
- URL removal statistics
- Token/character savings
- Inference timing
- Image processing details

## 🎉 Ready to Run!

The script is now fully updated with:
- ✅ Official InternVL3 implementation
- ✅ Direct prompt format support  
- ✅ URL-based image downloading
- ✅ Comprehensive output files
- ✅ Robust error handling
- ✅ Memory optimization

Just adjust the `max_prompts` value and run when you have GPU access!
