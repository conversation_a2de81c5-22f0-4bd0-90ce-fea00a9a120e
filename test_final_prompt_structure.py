#!/usr/bin/env python3
"""
Test script to examine the structure of final_prompt.jsonl
"""

import json

def load_and_examine_jsonl(file_path: str):
    """Load and examine the JSONL file structure"""
    print(f"📂 Examining {file_path}...")
    
    try:
        prompts = []
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        print(f"📄 File size: {len(content):,} characters")
        
        # Handle formatted JSON records (multi-line)
        brace_count = 0
        current_record = ""
        record_count = 0

        for line_num, line in enumerate(content.split('\n'), 1):
            if line.strip() == '{' and brace_count == 0:
                if current_record.strip():
                    try:
                        record = json.loads(current_record.strip())
                        prompts.append(record)
                        record_count += 1
                        print(f"✅ Parsed record {record_count}")
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON decode error in record {record_count}: {e}")
                current_record = line + '\n'
                brace_count = 1
            elif brace_count > 0:
                current_record += line + '\n'
                brace_count += line.count('{') - line.count('}')

                if brace_count == 0:
                    try:
                        record = json.loads(current_record.strip())
                        prompts.append(record)
                        record_count += 1
                        print(f"✅ Parsed record {record_count}")
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON decode error in record {record_count}: {e}")
                    current_record = ""

        # Handle any remaining record
        if current_record.strip():
            try:
                record = json.loads(current_record.strip())
                prompts.append(record)
                record_count += 1
                print(f"✅ Parsed final record {record_count}")
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error in final record: {e}")

        print(f"\n📊 SUMMARY:")
        print(f"Total records found: {len(prompts)}")
        
        if prompts:
            # Examine first record structure
            first_record = prompts[0]
            print(f"\n🔍 First record structure:")
            print(f"Keys: {list(first_record.keys())}")
            
            for key, value in first_record.items():
                if isinstance(value, str):
                    print(f"  {key}: {len(value):,} characters")
                    if len(value) > 200:
                        print(f"    Preview: {value[:200]}...")
                    else:
                        print(f"    Content: {value}")
                elif isinstance(value, list):
                    print(f"  {key}: {len(value)} items")
                    if value and isinstance(value[0], str):
                        print(f"    First item: {value[0]}")
                        if len(value) > 1:
                            print(f"    Last item: {value[-1]}")
                else:
                    print(f"  {key}: {type(value).__name__}")
            
            # Check if we have the expected keys
            expected_keys = ['prompt', 'response', 'images']
            missing_keys = [key for key in expected_keys if key not in first_record]
            extra_keys = [key for key in first_record.keys() if key not in expected_keys]
            
            if missing_keys:
                print(f"\n⚠️ Missing expected keys: {missing_keys}")
            if extra_keys:
                print(f"\n📝 Additional keys found: {extra_keys}")
            if not missing_keys:
                print(f"\n✅ All expected keys present: {expected_keys}")
        
        return prompts

    except Exception as e:
        print(f"❌ Error examining file: {str(e)}")
        return []

def main():
    """Main function"""
    print("🔍 Final Prompt JSONL Structure Examiner")
    print("=" * 50)
    
    prompts = load_and_examine_jsonl('final_prompt.jsonl')
    
    if prompts:
        print(f"\n🎉 Successfully loaded {len(prompts)} records")
        
        # Show statistics for all records
        if len(prompts) > 1:
            print(f"\n📈 Statistics across all records:")
            
            prompt_lengths = []
            response_lengths = []
            image_counts = []
            
            for i, record in enumerate(prompts):
                prompt_text = record.get('prompt', '')
                response_text = record.get('response', '')
                images = record.get('images', [])
                
                prompt_lengths.append(len(prompt_text))
                response_lengths.append(len(response_text))
                image_counts.append(len(images))
                
                print(f"  Record {i+1}:")
                print(f"    Prompt: {len(prompt_text):,} chars")
                print(f"    Response: {len(response_text):,} chars")
                print(f"    Images: {len(images)} URLs")
            
            print(f"\n📊 Averages:")
            print(f"  Prompt length: {sum(prompt_lengths)/len(prompt_lengths):,.0f} chars")
            print(f"  Response length: {sum(response_lengths)/len(response_lengths):,.0f} chars")
            print(f"  Image count: {sum(image_counts)/len(image_counts):.1f} images")
    else:
        print("❌ No records loaded")

if __name__ == "__main__":
    main()
