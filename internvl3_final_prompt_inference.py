#!/usr/bin/env python3
"""
InternVL3 Inference for final_prompt.jsonl
Downloads images from URLs and processes with InternVL3 model
Saves baseline.json and generated.json separately
"""

import json
import time
import requests
import os
import re
import math
import numpy as np
from datetime import datetime
from PIL import Image
from io import BytesIO
import torch
import torchvision.transforms as T
from transformers import AutoTokenizer, AutoModel, AutoConfig
from torchvision.transforms.functional import InterpolationMode
import logging
import warnings
warnings.filterwarnings("ignore")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Constants from official InternVL3 example
IMAGENET_MEAN = (0.485, 0.456, 0.406)
IMAGENET_STD = (0.229, 0.224, 0.225)

def build_transform(input_size):
    """Build image transformation pipeline"""
    MEAN, STD = IMAGENET_MEAN, IMAGENET_STD
    transform = T.Compose([
        T.<PERSON>da(lambda img: img.convert('RGB') if img.mode != 'RGB' else img),
        T<PERSON>Resize((input_size, input_size), interpolation=InterpolationMode.BICUBIC),
        T.ToTensor(),
        T.Normalize(mean=MEAN, std=STD)
    ])
    return transform

def find_closest_aspect_ratio(aspect_ratio, target_ratios, width, height, image_size):
    """Find the closest aspect ratio from target ratios"""
    best_ratio_diff = float('inf')
    best_ratio = (1, 1)
    area = width * height
    for ratio in target_ratios:
        target_aspect_ratio = ratio[0] / ratio[1]
        ratio_diff = abs(aspect_ratio - target_aspect_ratio)
        if ratio_diff < best_ratio_diff:
            best_ratio_diff = ratio_diff
            best_ratio = ratio
        elif ratio_diff == best_ratio_diff:
            if area > 0.5 * image_size * image_size * ratio[0] * ratio[1]:
                best_ratio = ratio
    return best_ratio

def dynamic_preprocess(image, min_num=1, max_num=12, image_size=448, use_thumbnail=False):
    """Dynamic preprocessing for InternVL3"""
    orig_width, orig_height = image.size
    aspect_ratio = orig_width / orig_height

    # Calculate target ratios
    target_ratios = set(
        (i, j) for n in range(min_num, max_num + 1)
        for i in range(1, n + 1)
        for j in range(1, n + 1)
        if i * j <= max_num and i * j >= min_num
    )
    target_ratios = sorted(target_ratios, key=lambda x: x[0] * x[1])

    # Find the closest aspect ratio
    target_aspect_ratio = find_closest_aspect_ratio(
        aspect_ratio, target_ratios, orig_width, orig_height, image_size
    )

    # Calculate target dimensions
    target_width = image_size * target_aspect_ratio[0]
    target_height = image_size * target_aspect_ratio[1]
    blocks = target_aspect_ratio[0] * target_aspect_ratio[1]

    # Resize the image
    resized_img = image.resize((target_width, target_height))
    processed_images = []

    for i in range(blocks):
        box = (
            (i % (target_width // image_size)) * image_size,
            (i // (target_width // image_size)) * image_size,
            ((i % (target_width // image_size)) + 1) * image_size,
            ((i // (target_width // image_size)) + 1) * image_size
        )
        # Split the image
        split_img = resized_img.crop(box)
        processed_images.append(split_img)

    assert len(processed_images) == blocks

    if use_thumbnail and len(processed_images) != 1:
        thumbnail_img = image.resize((image_size, image_size))
        processed_images.append(thumbnail_img)

    return processed_images

def load_image_from_url(image_url, input_size=448, max_num=12):
    """Load image from URL using official InternVL3 preprocessing with more tiles for A40"""
    try:
        # Download image
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(image_url, headers=headers, timeout=30)
        response.raise_for_status()

        image = Image.open(BytesIO(response.content)).convert('RGB')

        # Apply official InternVL3 preprocessing with more tiles for better GPU utilization
        transform = build_transform(input_size=input_size)
        images = dynamic_preprocess(
            image,
            image_size=input_size,
            use_thumbnail=True,
            max_num=max_num  # Increased from 6 to 12 for A40
        )
        pixel_values = [transform(img) for img in images]
        pixel_values = torch.stack(pixel_values)

        return pixel_values

    except Exception as e:
        logger.error(f"Failed to load image from {image_url}: {str(e)}")
        return None

def remove_urls_from_text(text: str):
    """Remove URLs from text to reduce token count"""
    url_patterns = [
        r'https?://[^\s<>"]+',
        r'www\.[^\s<>"]+',
        r'ftp://[^\s<>"]+',
        r'[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:/[^\s<>"]*)?',
    ]

    cleaned_text = text
    urls_removed = 0

    for pattern in url_patterns:
        matches = re.findall(pattern, cleaned_text)
        urls_removed += len(matches)
        cleaned_text = re.sub(pattern, '', cleaned_text)

    # Clean up whitespace
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
    cleaned_text = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_text)
    cleaned_text = cleaned_text.strip()

    return cleaned_text, urls_removed

# Removed chunking function - using full prompts as requested

def load_prompts_from_jsonl(file_path: str):
    """Load prompts from JSONL file with proper parsing"""
    print(f"📂 Loading prompts from {file_path}...")

    try:
        prompts = []
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Handle formatted JSON records (multi-line)
        brace_count = 0
        current_record = ""

        for line in content.split('\n'):
            if line.strip() == '{' and brace_count == 0:
                if current_record.strip():
                    try:
                        record = json.loads(current_record.strip())
                        prompts.append(record)
                    except json.JSONDecodeError:
                        pass
                current_record = line + '\n'
                brace_count = 1
            elif brace_count > 0:
                current_record += line + '\n'
                brace_count += line.count('{') - line.count('}')

                if brace_count == 0:
                    try:
                        record = json.loads(current_record.strip())
                        prompts.append(record)
                    except json.JSONDecodeError:
                        pass
                    current_record = ""

        # Handle any remaining record
        if current_record.strip():
            try:
                record = json.loads(current_record.strip())
                prompts.append(record)
            except json.JSONDecodeError:
                pass

        print(f"✅ Loaded {len(prompts)} prompts")
        return prompts

    except Exception as e:
        print(f"❌ Error loading prompts: {str(e)}")
        return []

def split_model_a40():
    """Create device map optimized for A40 GPU with InternVL3-9B"""
    device_map = {}
    world_size = torch.cuda.device_count()

    # For A40 48GB, we can handle InternVL3-9B without quantization
    # Use auto device mapping for single GPU
    return "auto"

def initialize_internvl3():
    """Initialize InternVL3-8B model using official approach for A40"""
    print("🤖 Initializing InternVL3-8B model for A40...")

    try:
        model_path = "OpenGVLab/InternVL3-8B"

        print("🔄 Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(
            model_path,
            trust_remote_code=True,
            use_fast=False
        )
        print("✅ Tokenizer loaded successfully")

        # Get device map for A40
        device_map = split_model_a40()

        print("🔄 Loading InternVL3-8B model without quantization for A40...")
        model = AutoModel.from_pretrained(
            model_path,
            torch_dtype=torch.bfloat16,
            load_in_8bit=False,  # No quantization for better GPU utilization
            low_cpu_mem_usage=True,
            use_flash_attn=True,
            trust_remote_code=True,
            device_map=device_map
        ).eval()

        print("✅ InternVL3-8B model loaded successfully")
        print(f"💾 Model device: {next(model.parameters()).device}")
        print(f"🔢 Model dtype: {next(model.parameters()).dtype}")

        # Check memory usage
        if torch.cuda.is_available():
            memory_allocated = torch.cuda.memory_allocated() / 1024**3
            memory_reserved = torch.cuda.memory_reserved() / 1024**3
            print(f"📊 A40 Memory - Allocated: {memory_allocated:.1f}GB, Reserved: {memory_reserved:.1f}GB")
            print(f"📊 A40 Usage: {memory_reserved/48*100:.1f}% of 48GB")

        return tokenizer, model

    except Exception as e:
        print(f"❌ Failed to load InternVL3-9B model: {str(e)}")
        print("💡 Trying fallback without flash attention...")

        try:
            # Fallback without flash attention
            model = AutoModel.from_pretrained(
                model_path,
                torch_dtype=torch.bfloat16,
                load_in_8bit=False,
                low_cpu_mem_usage=True,
                trust_remote_code=True,
                device_map="auto"
            ).eval()

            tokenizer = AutoTokenizer.from_pretrained(
                model_path,
                trust_remote_code=True,
                use_fast=False
            )

            print("✅ InternVL3-8B model loaded with fallback settings")
            return tokenizer, model

        except Exception as e2:
            print(f"❌ Fallback initialization also failed: {str(e2)}")
            print("💡 Troubleshooting:")
            print("  1. Install: pip install bitsandbytes flash-attn")
            print("  2. Check CUDA drivers")
            print("  3. Ensure sufficient VRAM (48GB A40)")
            return None, None

def process_images_for_internvl3(image_urls: list, max_images: int = 4):
    """Download and process images for InternVL3 using official preprocessing"""
    processed_images = []

    print(f"🖼️ Processing {len(image_urls)} image URLs (max {max_images})...")

    for i, url in enumerate(image_urls[:max_images]):
        print(f"  📥 Processing image {i+1}: {url}")

        pixel_values = load_image_from_url(url, input_size=448, max_num=6)
        if pixel_values is not None:
            processed_images.append(pixel_values)
            print(f"    ✅ Processed into {pixel_values.shape[0]} tiles: {pixel_values.shape}")
        else:
            print(f"    ❌ Failed to process")

    print(f"✅ Successfully processed {len(processed_images)} images")
    return processed_images

def run_internvl3_inference(tokenizer, model, prompt: str, processed_images: list):
    """Run inference with InternVL3-8B model - no chunking, accept long sequences"""
    try:
        # Combine images using official approach
        if len(processed_images) == 1:
            combined_pixel_values = processed_images[0]
            num_patches_list = [processed_images[0].size(0)]
        else:
            combined_pixel_values = torch.cat(processed_images, dim=0)
            num_patches_list = [img.size(0) for img in processed_images]

        # Move to GPU
        combined_pixel_values = combined_pixel_values.to(torch.bfloat16).cuda()

        print(f"📊 Combined images: {combined_pixel_values.shape}")
        print(f"📊 Patches per image: {num_patches_list}")

        # Create question using official format
        if len(processed_images) == 1:
            question = f"<image>\n{prompt}"
        else:
            # Multi-image format
            image_prefix = "".join([f"Image-{j+1}: <image>\n" for j in range(len(processed_images))])
            question = f"{image_prefix}{prompt}"

        prompt_tokens = len(tokenizer.encode(prompt, add_special_tokens=False))
        print(f"� Prompt tokens: {prompt_tokens:,} (may exceed model limit)")
        print(f"📝 Question length: {len(question):,} chars")

        # Generation config with 10k max tokens as requested
        generation_config = {
            'max_new_tokens': 10000,  # Set to 10k as requested
            'temperature': 0.3,
            'do_sample': True,
            'top_p': 0.9,
            'repetition_penalty': 1.05,
            'pad_token_id': tokenizer.eos_token_id
        }

        # Run inference using official chat method - accept token length warnings
        print("🚀 Running InternVL3-8B inference (ignoring token length warnings)...")

        if len(processed_images) == 1:
            # Single image inference
            response = model.chat(
                tokenizer,
                combined_pixel_values,
                question,
                generation_config
            )
        else:
            # Multi-image inference with num_patches_list
            response = model.chat(
                tokenizer,
                combined_pixel_values,
                question,
                generation_config,
                num_patches_list=num_patches_list
            )

        return response

    except Exception as e:
        logger.error(f"Inference failed: {str(e)}")
        return None

def clean_json_response(response_text: str) -> str:
    """Clean the generated response to extract valid JSON format"""
    if not response_text:
        return ""

    # Remove markdown code blocks
    response_text = re.sub(r'```json\s*', '', response_text)
    response_text = re.sub(r'```\s*$', '', response_text)
    response_text = re.sub(r'^```\s*', '', response_text)

    # Find JSON content between braces
    start_idx = response_text.find('{')
    if start_idx == -1:
        return response_text.strip()

    # Count braces to find the end of JSON
    brace_count = 0
    end_idx = start_idx

    for i, char in enumerate(response_text[start_idx:], start_idx):
        if char == '{':
            brace_count += 1
        elif char == '}':
            brace_count -= 1
            if brace_count == 0:
                end_idx = i + 1
                break

    # Extract the JSON portion
    json_content = response_text[start_idx:end_idx].strip()

    # Validate that it's proper JSON
    try:
        json.loads(json_content)
        return json_content
    except json.JSONDecodeError:
        return json_content

def validate_gpu_setup():
    """Validate GPU setup for InternVL3"""
    print("🔍 Checking GPU setup...")

    if not torch.cuda.is_available():
        print("❌ CUDA not available")
        return False

    gpu_count = torch.cuda.device_count()
    current_device = torch.cuda.current_device()
    gpu_name = torch.cuda.get_device_name(current_device)
    gpu_memory = torch.cuda.get_device_properties(current_device).total_memory / 1024**3

    print(f"✅ GPU Setup:")
    print(f"  Device: {gpu_name}")
    print(f"  Memory: {gpu_memory:.1f} GB")
    print(f"  CUDA Devices: {gpu_count}")

    if gpu_memory < 16:
        print("⚠️ Warning: Less than 16GB GPU memory detected")
        print("   InternVL3-8B may require memory optimization")

    return True

def main():
    """Main inference function"""
    print("🚀 InternVL3 Final Prompt Inference")
    print("=" * 50)

    # Validate GPU setup
    if not validate_gpu_setup():
        print("❌ GPU setup validation failed")
        return

    # Load prompts
    prompts = load_prompts_from_jsonl('final_prompt.jsonl')
    if not prompts:
        print("❌ No prompts loaded")
        return

    # Initialize model
    tokenizer, model = initialize_internvl3()
    if not tokenizer or not model:
        print("❌ Model initialization failed")
        return

    # Process prompts - simplified output
    actual_responses = []
    generated_responses = []

    max_prompts = min(len(prompts), 2)  # Process up to 2 prompts for testing

    print(f"\n🔄 Processing {max_prompts} prompts...")
    print("=" * 50)

    for i, prompt_data in enumerate(prompts[:max_prompts], 1):
        print(f"\n{'='*15} PROMPT {i}/{max_prompts} {'='*15}")

        try:
            # Extract data
            prompt_text = prompt_data.get('prompt', '')
            expected_response = prompt_data.get('response', '')
            image_urls = prompt_data.get('images', [])

            print(f"📋 Data extracted:")
            print(f"  Prompt: {len(prompt_text):,} chars")
            print(f"  Expected response: {len(expected_response):,} chars")
            print(f"  Image URLs: {len(image_urls)}")

            # Remove URLs from prompt to reduce token count
            cleaned_prompt, urls_removed = remove_urls_from_text(prompt_text)
            chars_saved = len(prompt_text) - len(cleaned_prompt)

            print(f"🔗 URL removal results:")
            print(f"  URLs removed: {urls_removed}")
            print(f"  Characters saved: {chars_saved:,}")
            print(f"  Final prompt: {len(cleaned_prompt):,} chars")

            # Download and process images using official InternVL3 method (more images for A40)
            processed_images = process_images_for_internvl3(image_urls, max_images=6)

            if not processed_images:
                print("❌ No valid images, skipping prompt")
                continue

            # Run inference
            start_time = time.time()

            generated_response = run_internvl3_inference(
                tokenizer, model, cleaned_prompt, processed_images
            )

            inference_time = time.time() - start_time

            if generated_response:
                # Clean the response
                cleaned_response = clean_json_response(generated_response)

                print(f"✅ Inference completed in {inference_time:.2f}s")
                print(f"📝 Generated: {len(cleaned_response):,} chars")
                print(f"📄 Preview: {cleaned_response[:200]}...")

                # Store simplified results
                actual_responses.append(expected_response)
                generated_responses.append(cleaned_response)

            else:
                print("❌ No response generated")
                # Still save actual response, empty generated response
                actual_responses.append(expected_response)
                generated_responses.append("")

        except Exception as e:
            print(f"❌ Error processing prompt {i}: {e}")

            # Still save what we can
            try:
                expected_response = prompt_data.get('response', '')
                actual_responses.append(expected_response)
                generated_responses.append("")
            except:
                actual_responses.append("")
                generated_responses.append("")

    # Save simplified results

    # Save response.json (actual responses only)
    with open('response.json', 'w', encoding='utf-8') as f:
        json.dump(actual_responses, f, indent=2, ensure_ascii=False)

    # Save generate.json (generated responses only)
    with open('generate.json', 'w', encoding='utf-8') as f:
        json.dump(generated_responses, f, indent=2, ensure_ascii=False)

    # Summary
    successful_count = len([r for r in generated_responses if r.strip()])

    print(f"\n📊 SUMMARY:")
    print("=" * 50)
    print(f"📊 Processed: {len(generated_responses)} prompts")
    print(f"✅ Successful: {successful_count}")
    print(f"🤖 Model: InternVL3-8B")
    print(f"🌡️ Temperature: 0.3")
    print(f"🔢 Max tokens: 10,000")

    if actual_responses:
        avg_actual_length = sum(len(r) for r in actual_responses) / len(actual_responses)
        print(f"📄 Average actual response: {avg_actual_length:,.0f} chars")

    if successful_count > 0:
        successful_responses = [r for r in generated_responses if r.strip()]
        avg_generated_length = sum(len(r) for r in successful_responses) / len(successful_responses)
        print(f"📝 Average generated response: {avg_generated_length:,.0f} chars")

    print(f"\n� Results saved:")
    print(f"  � Actual responses: response.json")
    print(f"  🤖 Generated responses: generate.json")
    print("🎉 InternVL3-8B inference completed!")

if __name__ == "__main__":
    main()
