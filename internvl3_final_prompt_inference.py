#!/usr/bin/env python3
"""
InternVL3 Inference for final_prompt.jsonl
Downloads images from URLs and processes with InternVL3 model
Saves baseline.json and generated.json separately
"""

import json
import time
import requests
import os
import re
from datetime import datetime
from PIL import Image
from io import BytesIO
import torch
from transformers import <PERSON>Tokenizer, AutoModel
from torchvision import transforms
import logging
import warnings
warnings.filterwarnings("ignore")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def download_image_from_url(url: str, timeout: int = 30) -> Image.Image:
    """Download image from URL and return PIL Image"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(url, headers=headers, timeout=timeout)
        response.raise_for_status()

        image = Image.open(BytesIO(response.content))
        if image.mode != 'RGB':
            image = image.convert('RGB')

        return image

    except Exception as e:
        logger.error(f"Failed to download image from {url}: {str(e)}")
        return None

def load_prompts_from_jsonl(file_path: str):
    """Load prompts from JSONL file with proper parsing"""
    print(f"📂 Loading prompts from {file_path}...")

    try:
        prompts = []
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Handle formatted JSON records (multi-line)
        brace_count = 0
        current_record = ""

        for line in content.split('\n'):
            if line.strip() == '{' and brace_count == 0:
                if current_record.strip():
                    try:
                        record = json.loads(current_record.strip())
                        prompts.append(record)
                    except json.JSONDecodeError:
                        pass
                current_record = line + '\n'
                brace_count = 1
            elif brace_count > 0:
                current_record += line + '\n'
                brace_count += line.count('{') - line.count('}')

                if brace_count == 0:
                    try:
                        record = json.loads(current_record.strip())
                        prompts.append(record)
                    except json.JSONDecodeError:
                        pass
                    current_record = ""

        # Handle any remaining record
        if current_record.strip():
            try:
                record = json.loads(current_record.strip())
                prompts.append(record)
            except json.JSONDecodeError:
                pass

        print(f"✅ Loaded {len(prompts)} prompts")
        return prompts

    except Exception as e:
        print(f"❌ Error loading prompts: {str(e)}")
        return []

def initialize_internvl3():
    """Initialize InternVL3-8B model"""
    print("🤖 Initializing InternVL3-8B model...")

    try:
        model_path = "OpenGVLab/InternVL3-8B"

        # Load tokenizer and model with proper settings
        tokenizer = AutoTokenizer.from_pretrained(
            model_path,
            trust_remote_code=True,
            use_fast=False
        )

        # Load model with 8-bit precision for memory efficiency
        model = AutoModel.from_pretrained(
            model_path,
            torch_dtype=torch.bfloat16,
            low_cpu_mem_usage=True,
            trust_remote_code=True,
            device_map="auto"
        )

        model.eval()

        print("✅ InternVL3-8B model loaded successfully")
        return tokenizer, model

    except Exception as e:
        print(f"❌ Failed to load InternVL3 model: {str(e)}")
        print("💡 Trying fallback initialization...")

        try:
            # Fallback without flash attention
            model = AutoModel.from_pretrained(
                model_path,
                torch_dtype=torch.float16,
                low_cpu_mem_usage=True,
                trust_remote_code=True,
                device_map="auto"
            )

            tokenizer = AutoTokenizer.from_pretrained(
                model_path,
                trust_remote_code=True
            )

            model.eval()
            print("✅ InternVL3-8B model loaded with fallback settings")
            return tokenizer, model

        except Exception as e2:
            print(f"❌ Fallback initialization also failed: {str(e2)}")
            return None, None

def process_images_for_internvl3(image_urls: list, max_images: int = 4):
    """Download and process images for InternVL3"""
    processed_images = []

    print(f"🖼️ Processing {len(image_urls)} image URLs (max {max_images})...")

    for i, url in enumerate(image_urls[:max_images]):
        print(f"  📥 Downloading image {i+1}: {url}")

        image = download_image_from_url(url)
        if image:
            # Resize image if too large to save memory
            max_size = 1024
            if max(image.size) > max_size:
                ratio = max_size / max(image.size)
                new_size = (int(image.size[0] * ratio), int(image.size[1] * ratio))
                image = image.resize(new_size, Image.Resampling.LANCZOS)
                print(f"    📏 Resized to: {image.size}")

            processed_images.append(image)
            print(f"    ✅ Downloaded: {image.size}")
        else:
            print(f"    ❌ Failed to download")

    print(f"✅ Successfully processed {len(processed_images)} images")
    return processed_images

def run_internvl3_inference(tokenizer, model, prompt: str, images: list):
    """Run inference with InternVL3 model"""
    try:
        # Use the model's chat method directly
        with torch.no_grad():
            response = model.chat(
                tokenizer=tokenizer,
                pixel_values=None,
                images=images,
                question=prompt,
                generation_config=dict(
                    num_beams=1,
                    max_new_tokens=5000,
                    do_sample=True,
                    temperature=0.3,
                    repetition_penalty=1.05,
                    pad_token_id=tokenizer.eos_token_id,
                )
            )

        return response

    except Exception as e:
        logger.error(f"Inference failed: {str(e)}")

        # Try alternative approach
        try:
            print("💡 Trying alternative inference method...")

            # Prepare image tokens
            image_tokens = "<image>" * len(images)
            full_prompt = f"{image_tokens}\n{prompt}"

            # Use generate method if chat fails
            inputs = tokenizer(full_prompt, return_tensors="pt")

            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    images=images,
                    max_new_tokens=5000,
                    temperature=0.3,
                    do_sample=True,
                    repetition_penalty=1.05,
                    pad_token_id=tokenizer.eos_token_id,
                )

            response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            # Remove the input prompt from response
            response = response.replace(full_prompt, "").strip()

            return response

        except Exception as e2:
            logger.error(f"Alternative inference also failed: {str(e2)}")
            return None

def clean_json_response(response_text: str) -> str:
    """Clean the generated response to extract valid JSON format"""
    if not response_text:
        return ""

    # Remove markdown code blocks
    response_text = re.sub(r'```json\s*', '', response_text)
    response_text = re.sub(r'```\s*$', '', response_text)
    response_text = re.sub(r'^```\s*', '', response_text)

    # Find JSON content between braces
    start_idx = response_text.find('{')
    if start_idx == -1:
        return response_text.strip()

    # Count braces to find the end of JSON
    brace_count = 0
    end_idx = start_idx

    for i, char in enumerate(response_text[start_idx:], start_idx):
        if char == '{':
            brace_count += 1
        elif char == '}':
            brace_count -= 1
            if brace_count == 0:
                end_idx = i + 1
                break

    # Extract the JSON portion
    json_content = response_text[start_idx:end_idx].strip()

    # Validate that it's proper JSON
    try:
        json.loads(json_content)
        return json_content
    except json.JSONDecodeError:
        return json_content

def validate_gpu_setup():
    """Validate GPU setup for InternVL3"""
    print("🔍 Checking GPU setup...")

    if not torch.cuda.is_available():
        print("❌ CUDA not available")
        return False

    gpu_count = torch.cuda.device_count()
    current_device = torch.cuda.current_device()
    gpu_name = torch.cuda.get_device_name(current_device)
    gpu_memory = torch.cuda.get_device_properties(current_device).total_memory / 1024**3

    print(f"✅ GPU Setup:")
    print(f"  Device: {gpu_name}")
    print(f"  Memory: {gpu_memory:.1f} GB")
    print(f"  CUDA Devices: {gpu_count}")

    if gpu_memory < 16:
        print("⚠️ Warning: Less than 16GB GPU memory detected")
        print("   InternVL3-8B may require memory optimization")

    return True

def main():
    """Main inference function"""
    print("🚀 InternVL3 Final Prompt Inference")
    print("=" * 50)

    # Validate GPU setup
    if not validate_gpu_setup():
        print("❌ GPU setup validation failed")
        return

    # Load prompts
    prompts = load_prompts_from_jsonl('final_prompt.jsonl')
    if not prompts:
        print("❌ No prompts loaded")
        return

    # Initialize model
    tokenizer, model = initialize_internvl3()
    if not tokenizer or not model:
        print("❌ Model initialization failed")
        return

    # Process prompts
    baseline_results = []
    generated_results = []
    input_prompts = []

    max_prompts = min(len(prompts), 2)  # Process up to 2 prompts for testing

    print(f"\n🔄 Processing {max_prompts} prompts...")
    print("=" * 50)

    for i, prompt_data in enumerate(prompts[:max_prompts], 1):
        print(f"\n{'='*15} PROMPT {i}/{max_prompts} {'='*15}")

        try:
            # Extract data
            prompt_text = prompt_data.get('prompt', '')
            expected_response = prompt_data.get('response', '')
            image_urls = prompt_data.get('images', [])

            print(f"📋 Data extracted:")
            print(f"  Prompt: {len(prompt_text):,} chars")
            print(f"  Expected response: {len(expected_response):,} chars")
            print(f"  Image URLs: {len(image_urls)}")

            # Download and process images
            processed_images = process_images_for_internvl3(image_urls, max_images=4)

            if not processed_images:
                print("❌ No valid images, skipping prompt")
                continue

            # Run inference
            print("🚀 Running InternVL3 inference...")
            start_time = time.time()

            generated_response = run_internvl3_inference(
                tokenizer, model, prompt_text, processed_images
            )

            inference_time = time.time() - start_time

            if generated_response:
                # Clean the response
                cleaned_response = clean_json_response(generated_response)

                print(f"✅ Inference completed in {inference_time:.2f}s")
                print(f"📝 Generated: {len(cleaned_response):,} chars")
                print(f"📄 Preview: {cleaned_response[:200]}...")

                # Store results
                prompt_id = f"prompt_{i}"

                # Baseline result (actual/expected response)
                baseline_results.append({
                    'prompt_id': prompt_id,
                    'response': expected_response,
                    'response_length': len(expected_response)
                })

                # Generated result
                generated_results.append({
                    'prompt_id': prompt_id,
                    'response': cleaned_response,
                    'response_length': len(cleaned_response),
                    'inference_time': inference_time,
                    'num_images': len(processed_images),
                    'model': 'InternVL3-8B',
                    'temperature': 0.3,
                    'success': True
                })

                # Input prompt for reference
                input_prompts.append({
                    'prompt_id': prompt_id,
                    'prompt': prompt_text,
                    'prompt_length': len(prompt_text),
                    'num_images': len(image_urls),
                    'image_urls': image_urls
                })

            else:
                print("❌ No response generated")

                # Still save baseline and failed generated result
                prompt_id = f"prompt_{i}"

                baseline_results.append({
                    'prompt_id': prompt_id,
                    'response': expected_response,
                    'response_length': len(expected_response)
                })

                generated_results.append({
                    'prompt_id': prompt_id,
                    'response': '',
                    'response_length': 0,
                    'inference_time': inference_time,
                    'num_images': len(processed_images),
                    'model': 'InternVL3-8B',
                    'temperature': 0.3,
                    'success': False,
                    'error': 'No response generated'
                })

                input_prompts.append({
                    'prompt_id': prompt_id,
                    'prompt': prompt_text,
                    'prompt_length': len(prompt_text),
                    'num_images': len(image_urls),
                    'image_urls': image_urls
                })

        except Exception as e:
            print(f"❌ Error processing prompt {i}: {e}")

            # Still save what we can
            prompt_id = f"prompt_{i}"

            try:
                expected_response = prompt_data.get('response', '')
                prompt_text = prompt_data.get('prompt', '')
                image_urls = prompt_data.get('images', [])

                baseline_results.append({
                    'prompt_id': prompt_id,
                    'response': expected_response,
                    'response_length': len(expected_response)
                })

                generated_results.append({
                    'prompt_id': prompt_id,
                    'response': '',
                    'response_length': 0,
                    'inference_time': 0,
                    'num_images': 0,
                    'model': 'InternVL3-8B',
                    'temperature': 0.3,
                    'success': False,
                    'error': str(e)
                })

                input_prompts.append({
                    'prompt_id': prompt_id,
                    'prompt': prompt_text,
                    'prompt_length': len(prompt_text),
                    'num_images': len(image_urls),
                    'image_urls': image_urls
                })

            except:
                pass

    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Save baseline.json (actual/expected responses)
    baseline_file = f'baseline_{timestamp}.json'
    with open(baseline_file, 'w', encoding='utf-8') as f:
        json.dump(baseline_results, f, indent=2, ensure_ascii=False)

    # Save generated.json (model responses)
    generated_file = f'generated_{timestamp}.json'
    with open(generated_file, 'w', encoding='utf-8') as f:
        json.dump(generated_results, f, indent=2, ensure_ascii=False)

    # Save input prompts for reference
    prompts_file = f'input_prompts_{timestamp}.json'
    with open(prompts_file, 'w', encoding='utf-8') as f:
        json.dump(input_prompts, f, indent=2, ensure_ascii=False)

    # Summary
    successful = [r for r in generated_results if r.get('success', False)]

    print(f"\n📊 SUMMARY:")
    print("=" * 50)
    print(f"📊 Processed: {len(generated_results)} prompts")
    print(f"✅ Successful: {len(successful)}")
    print(f"🤖 Model: InternVL3-8B")
    print(f"🌡️ Temperature: 0.3")

    if successful:
        avg_time = sum(r['inference_time'] for r in successful) / len(successful)
        avg_generated_length = sum(r['response_length'] for r in successful) / len(successful)
        avg_baseline_length = sum(r['response_length'] for r in baseline_results) / len(baseline_results)

        print(f"⏱️ Average inference time: {avg_time:.2f}s")
        print(f"📝 Average generated response: {avg_generated_length:,.0f} chars")
        print(f"📄 Average baseline response: {avg_baseline_length:,.0f} chars")

    print(f"\n💾 Results saved:")
    print(f"  📄 Baseline responses: {baseline_file}")
    print(f"  🤖 Generated responses: {generated_file}")
    print(f"  📝 Input prompts: {prompts_file}")
    print("🎉 InternVL3 inference completed!")

if __name__ == "__main__":
    main()
