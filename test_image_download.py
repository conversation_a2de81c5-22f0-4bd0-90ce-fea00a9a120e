#!/usr/bin/env python3
"""
Test script to verify image downloading functionality
"""

import json
import requests
from PIL import Image
from io import BytesIO

def download_image_from_url(url: str, timeout: int = 30) -> Image.Image:
    """Download image from URL and return PIL Image"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=timeout)
        response.raise_for_status()
        
        image = Image.open(BytesIO(response.content))
        if image.mode != 'RGB':
            image = image.convert('RGB')
            
        return image
        
    except Exception as e:
        print(f"Failed to download image from {url}: {str(e)}")
        return None

def load_prompts_from_jsonl(file_path: str):
    """Load prompts from JSONL file with proper parsing"""
    print(f"📂 Loading prompts from {file_path}...")
    
    try:
        prompts = []
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Handle formatted JSON records (multi-line)
        brace_count = 0
        current_record = ""

        for line in content.split('\n'):
            if line.strip() == '{' and brace_count == 0:
                if current_record.strip():
                    try:
                        record = json.loads(current_record.strip())
                        prompts.append(record)
                    except json.JSONDecodeError:
                        pass
                current_record = line + '\n'
                brace_count = 1
            elif brace_count > 0:
                current_record += line + '\n'
                brace_count += line.count('{') - line.count('}')

                if brace_count == 0:
                    try:
                        record = json.loads(current_record.strip())
                        prompts.append(record)
                    except json.JSONDecodeError:
                        pass
                    current_record = ""

        # Handle any remaining record
        if current_record.strip():
            try:
                record = json.loads(current_record.strip())
                prompts.append(record)
            except json.JSONDecodeError:
                pass

        print(f"✅ Loaded {len(prompts)} prompts")
        return prompts

    except Exception as e:
        print(f"❌ Error loading prompts: {str(e)}")
        return []

def test_image_downloads():
    """Test downloading images from the first few records"""
    print("🧪 Testing Image Download Functionality")
    print("=" * 50)
    
    # Load prompts
    prompts = load_prompts_from_jsonl('final_prompt.jsonl')
    if not prompts:
        print("❌ No prompts loaded")
        return
    
    # Test first 3 records
    test_records = min(len(prompts), 3)
    
    for i, prompt_data in enumerate(prompts[:test_records], 1):
        print(f"\n{'='*15} RECORD {i}/{test_records} {'='*15}")
        
        image_urls = prompt_data.get('images', [])
        print(f"📋 Found {len(image_urls)} image URLs")
        
        # Test downloading first 2 images from each record
        max_test_images = min(len(image_urls), 2)
        successful_downloads = 0
        
        for j, url in enumerate(image_urls[:max_test_images]):
            print(f"\n  🖼️ Testing image {j+1}: {url}")
            
            image = download_image_from_url(url)
            if image:
                successful_downloads += 1
                print(f"    ✅ Success: {image.size}, mode: {image.mode}")
                
                # Resize test
                max_size = 1024
                if max(image.size) > max_size:
                    ratio = max_size / max(image.size)
                    new_size = (int(image.size[0] * ratio), int(image.size[1] * ratio))
                    resized_image = image.resize(new_size, Image.Resampling.LANCZOS)
                    print(f"    📏 Resize test: {image.size} -> {resized_image.size}")
                else:
                    print(f"    📏 No resize needed: {image.size}")
            else:
                print(f"    ❌ Failed to download")
        
        print(f"\n  📊 Record {i} summary: {successful_downloads}/{max_test_images} images downloaded successfully")
    
    print(f"\n🎉 Image download test completed!")

def test_json_parsing():
    """Test JSON response parsing"""
    print("\n🧪 Testing JSON Response Parsing")
    print("=" * 50)
    
    # Test cases
    test_responses = [
        '```json\n{"test": "value"}\n```',
        '{"test": "value"}',
        'Some text before\n```json\n{"test": "value"}\n```\nSome text after',
        '{"nested": {"key": "value", "number": 123}}',
        'Invalid JSON here',
    ]
    
    import re
    
    def clean_json_response(response_text: str) -> str:
        """Clean the generated response to extract valid JSON format"""
        if not response_text:
            return ""
        
        # Remove markdown code blocks
        response_text = re.sub(r'```json\s*', '', response_text)
        response_text = re.sub(r'```\s*$', '', response_text)
        response_text = re.sub(r'^```\s*', '', response_text)
        
        # Find JSON content between braces
        start_idx = response_text.find('{')
        if start_idx == -1:
            return response_text.strip()
        
        # Count braces to find the end of JSON
        brace_count = 0
        end_idx = start_idx
        
        for i, char in enumerate(response_text[start_idx:], start_idx):
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    end_idx = i + 1
                    break
        
        # Extract the JSON portion
        json_content = response_text[start_idx:end_idx].strip()
        
        # Validate that it's proper JSON
        try:
            json.loads(json_content)
            return json_content
        except json.JSONDecodeError:
            return json_content
    
    for i, test_response in enumerate(test_responses, 1):
        print(f"\n  Test {i}: {test_response[:50]}...")
        cleaned = clean_json_response(test_response)
        print(f"    Cleaned: {cleaned}")
        
        try:
            parsed = json.loads(cleaned)
            print(f"    ✅ Valid JSON: {type(parsed).__name__}")
        except json.JSONDecodeError:
            print(f"    ❌ Invalid JSON")

def main():
    """Main test function"""
    test_image_downloads()
    test_json_parsing()

if __name__ == "__main__":
    main()
