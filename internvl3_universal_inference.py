#!/usr/bin/env python3
"""
Universal InternVL3-8B Inference
Automatically adapts to any GPU configuration
"""

import json
import time
import math
import numpy as np
import torch
import torchvision.transforms as T
from datetime import datetime
from io import BytesIO
from PIL import Image
from torchvision.transforms.functional import InterpolationMode
from transformers import AutoModel, AutoTokenizer, AutoConfig
import base64
import re
import jsonschema

# Import existing utilities
from utils.data_loader import load_prompts_from_jsonl

# Constants from official example
IMAGENET_MEAN = (0.485, 0.456, 0.406)
IMAGENET_STD = (0.229, 0.224, 0.225)

def detect_gpu_configuration():
    """Detect GPU configuration and recommend optimal settings"""
    if not torch.cuda.is_available():
        return {
            'device': 'cpu',
            'gpu_count': 0,
            'total_vram': 0,
            'recommended_precision': 'float32',
            'max_images': 1,
            'max_tiles': 2,
            'batch_size': 1
        }

    gpu_count = torch.cuda.device_count()
    gpu_name = torch.cuda.get_device_name(0)

    # Get VRAM for primary GPU
    gpu_properties = torch.cuda.get_device_properties(0)
    total_vram_gb = gpu_properties.total_memory / (1024**3)

    print(f"🔍 GPU Detection:")
    print(f"  GPU Count: {gpu_count}")
    print(f"  Primary GPU: {gpu_name}")
    print(f"  VRAM: {total_vram_gb:.1f}GB")

    # Configure based on VRAM and GPU type
    if total_vram_gb >= 40:  # A100 80GB, A40 48GB, etc.
        config = {
            'device': 'cuda',
            'gpu_count': gpu_count,
            'total_vram': total_vram_gb,
            'recommended_precision': '8bit',
            'max_images': 4,
            'max_tiles': 8,
            'batch_size': 1,
            'use_flash_attn': True,
            'device_map': 'auto'
        }
        print(f"  🚀 High-end GPU detected: Optimal settings")

    elif total_vram_gb >= 20:  # RTX 4090 24GB, RTX 3090 24GB, etc.
        config = {
            'device': 'cuda',
            'gpu_count': gpu_count,
            'total_vram': total_vram_gb,
            'recommended_precision': '8bit',
            'max_images': 3,
            'max_tiles': 6,
            'batch_size': 1,
            'use_flash_attn': True,
            'device_map': 'auto'
        }
        print(f"  💪 Mid-range GPU detected: Balanced settings")

    elif total_vram_gb >= 10:  # RTX 3080 12GB, RTX 4070 12GB, etc.
        config = {
            'device': 'cuda',
            'gpu_count': gpu_count,
            'total_vram': total_vram_gb,
            'recommended_precision': '8bit',
            'max_images': 2,
            'max_tiles': 4,
            'batch_size': 1,
            'use_flash_attn': False,  # May cause issues on lower VRAM
            'device_map': 'auto'
        }
        print(f"  ⚡ Entry-level GPU detected: Conservative settings")

    else:  # < 10GB VRAM
        config = {
            'device': 'cuda',
            'gpu_count': gpu_count,
            'total_vram': total_vram_gb,
            'recommended_precision': '4bit',  # Aggressive quantization
            'max_images': 1,
            'max_tiles': 2,
            'batch_size': 1,
            'use_flash_attn': False,
            'device_map': 'auto'
        }
        print(f"  🔋 Low VRAM GPU detected: Minimal settings")

    return config

def create_adaptive_device_map(gpu_config):
    """Create device map based on GPU configuration"""
    if gpu_config['gpu_count'] <= 1:
        return 'auto'

    # Multi-GPU setup
    try:
        from transformers import AutoConfig
        model_config = AutoConfig.from_pretrained("OpenGVLab/InternVL3-8B", trust_remote_code=True)

        if hasattr(model_config, 'llm_config') and hasattr(model_config.llm_config, 'num_hidden_layers'):
            num_layers = model_config.llm_config.num_hidden_layers
        else:
            num_layers = 32  # Default estimate

        device_map = {}
        gpu_count = gpu_config['gpu_count']

        # Distribute layers across GPUs
        layers_per_gpu = math.ceil(num_layers / gpu_count)

        for i in range(num_layers):
            gpu_id = i // layers_per_gpu
            device_map[f'language_model.model.layers.{i}'] = min(gpu_id, gpu_count - 1)

        # Place other components on GPU 0
        device_map['vision_model'] = 0
        device_map['mlp1'] = 0
        device_map['language_model.model.tok_embeddings'] = 0
        device_map['language_model.model.embed_tokens'] = 0
        device_map['language_model.output'] = 0
        device_map['language_model.model.norm'] = 0
        device_map['language_model.lm_head'] = 0

        print(f"📊 Multi-GPU device map created for {gpu_count} GPUs")
        return device_map

    except Exception as e:
        print(f"⚠️ Multi-GPU setup failed, using auto: {e}")
        return 'auto'

def initialize_internvl3_universal(gpu_config):
    """Initialize InternVL3-8B with universal GPU support"""
    model_path = 'OpenGVLab/InternVL3-8B'
    precision = gpu_config['recommended_precision']

    print(f"🤖 Initializing InternVL3-8B for {gpu_config['total_vram']:.1f}GB GPU")
    print(f"🔢 Using {precision} precision")

    try:
        # Create device map
        device_map = create_adaptive_device_map(gpu_config)

        # Load tokenizer
        print("🔄 Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(
            model_path,
            trust_remote_code=True,
            use_fast=False
        )
        print("✅ Tokenizer loaded successfully")

        # Load model with adaptive settings
        print(f"🔄 Loading model with {precision} precision...")

        if precision == '4bit':
            from transformers import BitsAndBytesConfig
            quantization_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_compute_dtype=torch.bfloat16,
                bnb_4bit_quant_type="nf4",
                bnb_4bit_use_double_quant=True,
            )

            model = AutoModel.from_pretrained(
                model_path,
                quantization_config=quantization_config,
                torch_dtype=torch.bfloat16,
                low_cpu_mem_usage=True,
                use_flash_attn=gpu_config['use_flash_attn'],
                trust_remote_code=True,
                device_map=device_map
            ).eval()
            print("✅ Model loaded with 4-bit quantization")

        elif precision == '8bit':
            model = AutoModel.from_pretrained(
                model_path,
                torch_dtype=torch.bfloat16,
                load_in_8bit=True,
                low_cpu_mem_usage=True,
                use_flash_attn=gpu_config['use_flash_attn'],
                trust_remote_code=True,
                device_map=device_map
            ).eval()
            print("✅ Model loaded with 8-bit quantization")

        else:  # float32 or bfloat16
            model = AutoModel.from_pretrained(
                model_path,
                torch_dtype=torch.bfloat16,
                low_cpu_mem_usage=True,
                use_flash_attn=gpu_config['use_flash_attn'],
                trust_remote_code=True,
                device_map=device_map
            ).eval()
            print("✅ Model loaded with bfloat16 (no quantization)")

        # Get model dtype
        model_dtype = next(model.parameters()).dtype
        print(f"💾 Model device: {next(model.parameters()).device}")
        print(f"🔢 Model dtype: {model_dtype}")

        # Check memory usage
        if torch.cuda.is_available():
            memory_allocated = torch.cuda.memory_allocated() / 1024**3
            memory_reserved = torch.cuda.memory_reserved() / 1024**3
            memory_percent = (memory_reserved / gpu_config['total_vram']) * 100
            print(f"📊 GPU Memory - Allocated: {memory_allocated:.1f}GB, Reserved: {memory_reserved:.1f}GB")
            print(f"📊 Memory Usage: {memory_percent:.1f}% of {gpu_config['total_vram']:.1f}GB")

        return model, tokenizer, model_dtype, gpu_config

    except Exception as e:
        print(f"❌ Error loading InternVL3-8B model: {e}")

        # Provide GPU-specific error messages
        if "CUDA out of memory" in str(e):
            print(f"\n🚨 GPU Memory Error:")
            print(f"1. Try reducing max_tiles: {gpu_config['max_tiles']} → {gpu_config['max_tiles']//2}")
            print(f"2. Try reducing max_images: {gpu_config['max_images']} → {gpu_config['max_images']//2}")
            if precision != '4bit':
                print(f"3. Try 4-bit quantization for lower memory usage")
        elif "flash_attn" in str(e):
            print(f"\n🚨 Flash Attention Error:")
            print(f"Try installing: pip install flash-attn --no-build-isolation")
            print(f"Or disable: use_flash_attn=False")
        elif "bitsandbytes" in str(e):
            print(f"\n🚨 BitsAndBytes Error:")
            print(f"Try installing: pip install bitsandbytes")

        raise e

def build_transform(input_size):
    """Build image transformation pipeline"""
    MEAN, STD = IMAGENET_MEAN, IMAGENET_STD
    transform = T.Compose([
        T.Lambda(lambda img: img.convert('RGB') if img.mode != 'RGB' else img),
        T.Resize((input_size, input_size), interpolation=InterpolationMode.BICUBIC),
        T.ToTensor(),
        T.Normalize(mean=MEAN, std=STD)
    ])
    return transform

def find_closest_aspect_ratio(aspect_ratio, target_ratios, width, height, image_size):
    """Find the closest aspect ratio from target ratios"""
    best_ratio_diff = float('inf')
    best_ratio = (1, 1)
    area = width * height
    for ratio in target_ratios:
        target_aspect_ratio = ratio[0] / ratio[1]
        ratio_diff = abs(aspect_ratio - target_aspect_ratio)
        if ratio_diff < best_ratio_diff:
            best_ratio_diff = ratio_diff
            best_ratio = ratio
        elif ratio_diff == best_ratio_diff:
            if area > 0.5 * image_size * image_size * ratio[0] * ratio[1]:
                best_ratio = ratio
    return best_ratio

def dynamic_preprocess(image, min_num=1, max_num=12, image_size=448, use_thumbnail=False):
    """Dynamic preprocessing for InternVL3"""
    orig_width, orig_height = image.size
    aspect_ratio = orig_width / orig_height

    # Calculate target ratios
    target_ratios = set(
        (i, j) for n in range(min_num, max_num + 1)
        for i in range(1, n + 1)
        for j in range(1, n + 1)
        if i * j <= max_num and i * j >= min_num
    )
    target_ratios = sorted(target_ratios, key=lambda x: x[0] * x[1])

    # Find the closest aspect ratio
    target_aspect_ratio = find_closest_aspect_ratio(
        aspect_ratio, target_ratios, orig_width, orig_height, image_size
    )

    # Calculate target dimensions
    target_width = image_size * target_aspect_ratio[0]
    target_height = image_size * target_aspect_ratio[1]
    blocks = target_aspect_ratio[0] * target_aspect_ratio[1]

    # Resize the image
    resized_img = image.resize((target_width, target_height))
    processed_images = []

    for i in range(blocks):
        box = (
            (i % (target_width // image_size)) * image_size,
            (i // (target_width // image_size)) * image_size,
            ((i % (target_width // image_size)) + 1) * image_size,
            ((i // (target_width // image_size)) + 1) * image_size
        )
        split_img = resized_img.crop(box)
        processed_images.append(split_img)

    assert len(processed_images) == blocks

    if use_thumbnail and len(processed_images) != 1:
        thumbnail_img = image.resize((image_size, image_size))
        processed_images.append(thumbnail_img)

    return processed_images

def load_image_from_base64(base64_data, input_size=448, max_num=6, model_dtype=torch.bfloat16):
    """Load image from base64 data using official preprocessing"""
    try:
        if base64_data.startswith('data:'):
            header, base64_content = base64_data.split(',', 1)
        else:
            base64_content = base64_data.strip()

        image_bytes = base64.b64decode(base64_content)
        image = Image.open(BytesIO(image_bytes)).convert('RGB')

        # Apply official InternVL3 preprocessing
        transform = build_transform(input_size=input_size)
        images = dynamic_preprocess(
            image,
            image_size=input_size,
            use_thumbnail=True,
            max_num=max_num
        )
        pixel_values = [transform(img) for img in images]
        pixel_values = torch.stack(pixel_values)

        # Convert to match model dtype exactly
        pixel_values = pixel_values.to(model_dtype)

        print(f"    📏 Processed into {len(images)} tiles: {pixel_values.shape}")
        print(f"    🔢 Image tensor dtype: {pixel_values.dtype}")
        return pixel_values

    except Exception as e:
        print(f"    ❌ Image processing error: {e}")
        return None

def load_response_schema():
    """Load the response schema for validation"""
    try:
        with open('response_schema.json', 'r') as f:
            schema = json.load(f)
        return schema
    except Exception as e:
        print(f"⚠️ Could not load response schema: {e}")
        return None

def validate_json_response(response_text, schema=None):
    """Validate if response is valid JSON and follows schema"""
    try:
        # Try to parse as JSON
        parsed_json = json.loads(response_text)

        # Validate against schema if provided
        if schema:
            jsonschema.validate(parsed_json, schema)
            return parsed_json, True, "Valid JSON following schema"
        else:
            return parsed_json, True, "Valid JSON (schema not available)"

    except json.JSONDecodeError as e:
        return None, False, f"Invalid JSON: {e}"
    except jsonschema.ValidationError as e:
        return None, False, f"Schema validation failed: {e.message}"
    except Exception as e:
        return None, False, f"Validation error: {e}"

def extract_json_from_response(response_text):
    """Extract JSON from response text that might contain extra text"""
    json_patterns = [
        r'\{.*\}',  # Simple JSON object
        r'\[.*\]',  # JSON array
    ]

    for pattern in json_patterns:
        matches = re.findall(pattern, response_text, re.DOTALL)
        if matches:
            largest_match = max(matches, key=len)
            try:
                parsed = json.loads(largest_match)
                return largest_match, parsed
            except:
                continue

    return None, None

def remove_urls_from_text(text: str):
    """Remove URLs from text to reduce token count"""
    url_patterns = [
        r'https?://[^\s<>"]+',
        r'www\.[^\s<>"]+',
        r'ftp://[^\s<>"]+',
        r'[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:/[^\s<>"]*)?',
    ]

    cleaned_text = text
    urls_removed = 0

    for pattern in url_patterns:
        matches = re.findall(pattern, cleaned_text)
        urls_removed += len(matches)
        cleaned_text = re.sub(pattern, '', cleaned_text)

    # Clean up whitespace
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
    cleaned_text = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_text)
    cleaned_text = cleaned_text.strip()

    return cleaned_text, urls_removed

def main():
    """Universal InternVL3-8B inference for any GPU configuration"""
    print("🌟 Universal InternVL3-8B Vision-Language Model Inference")
    print("=" * 60)
    print("🎯 Automatically adapts to any GPU configuration")

    # Detect GPU configuration
    print("\n🔍 DETECTING GPU CONFIGURATION")
    print("-" * 50)
    gpu_config = detect_gpu_configuration()

    if gpu_config['device'] == 'cpu':
        print("❌ No CUDA GPU detected. InternVL3-8B requires GPU.")
        print("💡 Please ensure CUDA is installed and GPU is available.")
        return

    # Load response schema for validation
    print("\n📋 Loading response schema...")
    response_schema = load_response_schema()
    if response_schema:
        print("✅ Response schema loaded for validation")
    else:
        print("⚠️ Response schema not found - will validate JSON format only")

    # Load prompts
    input_files = ['final_image_prompts_cleaned.jsonl', 'scrape_content_prompts.jsonl']
    prompts = None

    for input_file in input_files:
        prompts = load_prompts_from_jsonl(input_file)
        if prompts:
            print(f"✅ Using prompts from: {input_file}")
            break

    if not prompts:
        print("❌ No prompts found")
        return

    # Initialize model with GPU-adaptive settings
    print(f"\n🤖 INITIALIZING INTERNVL3-8B")
    print("-" * 50)

    try:
        model, tokenizer, model_dtype, final_gpu_config = initialize_internvl3_universal(gpu_config)
        print(f"✅ InternVL3-8B ready for {final_gpu_config['total_vram']:.1f}GB GPU")

    except Exception as e:
        print(f"❌ Model initialization failed: {e}")
        print("💡 GPU-specific troubleshooting:")
        print("  1. Check CUDA drivers and compatibility")
        print("  2. Try reducing precision (8bit → 4bit)")
        print("  3. Ensure sufficient VRAM available")
        return

    # Generation config adapted to GPU
    max_tokens = min(2000, 4000 if gpu_config['total_vram'] >= 40 else 1500)
    generation_config = {
        'max_new_tokens': max_tokens,
        'temperature': 0.3,
        'do_sample': True,
        'top_p': 0.9,
        'repetition_penalty': 1.05
    }

    print(f"🎛️ Generation config (adapted): max_tokens={max_tokens}")

    # Process prompts with GPU-adaptive limits
    results = []
    max_prompts = min(len(prompts), 5)

    print(f"\n🚀 PROCESSING {max_prompts} PROMPTS")
    print("=" * 60)
    print(f"📊 GPU Limits: {gpu_config['max_images']} images, {gpu_config['max_tiles']} tiles each")

    for i, prompt_data in enumerate(prompts[:max_prompts], 1):
        print(f"\n{'='*15} PROMPT {i}/{max_prompts} {'='*15}")

        try:
            # Extract data
            system_prompt = prompt_data.get('SystemPrompt', '')
            user_prompt = prompt_data.get('UserPrompt', '')
            image_data_urls = prompt_data.get('Images', [])

            print(f"📋 Original lengths:")
            print(f"  System: {len(system_prompt):,} chars")
            print(f"  User: {len(user_prompt):,} chars")
            print(f"  Images: {len(image_data_urls)}")

            # Remove URLs for efficiency
            cleaned_system, system_urls = remove_urls_from_text(system_prompt)
            cleaned_user, user_urls = remove_urls_from_text(user_prompt)

            total_urls = system_urls + user_urls
            total_saved = (len(system_prompt) - len(cleaned_system)) + (len(user_prompt) - len(cleaned_user))

            print(f"🔗 URL removal results:")
            print(f"  URLs removed: {total_urls}")
            print(f"  Characters saved: {total_saved:,}")

            # Process images with GPU-adaptive limits
            processed_images = []
            max_images_to_process = min(len(image_data_urls), gpu_config['max_images'])
            print(f"🖼️ Processing {max_images_to_process} images (GPU limit: {gpu_config['max_images']})...")

            for j, img_data in enumerate(image_data_urls[:max_images_to_process]):
                pixel_values = load_image_from_base64(
                    img_data,
                    input_size=448,
                    max_num=gpu_config['max_tiles'],  # GPU-adaptive tile limit
                    model_dtype=model_dtype
                )
                if pixel_values is not None:
                    processed_images.append(pixel_values)
                    print(f"  ✓ Image {j+1}: {pixel_values.shape}")
                else:
                    print(f"  ✗ Image {j+1}: failed")

            if not processed_images:
                print("❌ No valid images, skipping prompt")
                continue

            # Combine images
            if len(processed_images) == 1:
                combined_pixel_values = processed_images[0]
                num_patches_list = [processed_images[0].size(0)]
            else:
                combined_pixel_values = torch.cat(processed_images, dim=0)
                num_patches_list = [img.size(0) for img in processed_images]

            # Move to GPU
            device = next(model.parameters()).device
            combined_pixel_values = combined_pixel_values.to(device)

            print(f"📊 Combined images: {combined_pixel_values.shape}")
            print(f"📊 Patches per image: {num_patches_list}")

            # Create question (Phi-3-Vision style)
            if len(processed_images) == 1:
                question = f"<image>\n{cleaned_system}\n\n{cleaned_user}"
            else:
                image_prefix = "".join([f"Image-{j+1}: <image>\n" for j in range(len(processed_images))])
                question = f"{image_prefix}{cleaned_system}\n\n{cleaned_user}"

            print(f"📝 Question length: {len(question):,} chars")

            # Run inference
            print(f"🚀 Running inference on {gpu_config['total_vram']:.1f}GB GPU...")
            start_time = time.time()

            try:
                if len(processed_images) == 1:
                    response = model.chat(tokenizer, combined_pixel_values, question, generation_config)
                else:
                    response = model.chat(tokenizer, combined_pixel_values, question, generation_config, num_patches_list=num_patches_list)

                inference_time = time.time() - start_time

                print(f"✅ Inference completed in {inference_time:.2f}s")
                print(f"📝 Generated: {len(str(response)):,} chars")

                # Handle empty responses
                if not response or len(str(response).strip()) == 0:
                    print("❌ Empty response - trying fallback...")
                    fallback_question = f"{question}\n\nPlease respond in valid JSON format."

                    if len(processed_images) == 1:
                        response = model.chat(tokenizer, combined_pixel_values, fallback_question, generation_config)
                    else:
                        response = model.chat(tokenizer, combined_pixel_values, fallback_question, generation_config, num_patches_list=num_patches_list)

                    if response and len(str(response).strip()) > 0:
                        print("✅ Fallback approach worked!")
                    else:
                        print("❌ Fallback also failed - skipping prompt")
                        continue

                print(f"📄 Preview: {str(response)[:300]}...")

                # Validate response format
                print("🔍 Validating response format...")
                parsed_json, is_valid, validation_message = validate_json_response(str(response), response_schema)

                if not is_valid:
                    print(f"⚠️ Response validation failed: {validation_message}")
                    extracted_json_text, extracted_json = extract_json_from_response(str(response))
                    if extracted_json:
                        print("🔧 Extracted JSON from response")
                        parsed_json, is_valid, validation_message = validate_json_response(extracted_json_text, response_schema)
                        if is_valid:
                            response = extracted_json_text
                            print("✅ Extracted JSON is valid")
                        else:
                            print(f"❌ Extracted JSON still invalid: {validation_message}")
                    else:
                        print("❌ Could not extract valid JSON from response")
                else:
                    print(f"✅ Response validation: {validation_message}")

                # Store results
                result = {
                    'prompt_id': f"prompt_{i}",
                    'model': "OpenGVLab/InternVL3-8B",
                    'gpu_config': {
                        'gpu_name': torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'Unknown',
                        'total_vram': gpu_config['total_vram'],
                        'precision': gpu_config['recommended_precision'],
                        'max_images': gpu_config['max_images'],
                        'max_tiles': gpu_config['max_tiles']
                    },
                    'original_system_length': len(system_prompt),
                    'original_user_length': len(user_prompt),
                    'cleaned_system_length': len(cleaned_system),
                    'cleaned_user_length': len(cleaned_user),
                    'urls_removed': total_urls,
                    'chars_saved': total_saved,
                    'num_images': len(processed_images),
                    'num_patches_list': num_patches_list,
                    'total_patches': sum(num_patches_list),
                    'generated_response': str(response),
                    'generated_length': len(str(response)),
                    'inference_time': inference_time,
                    'generation_config': generation_config,
                    'schema_validation': {
                        'is_valid_json': is_valid,
                        'validation_message': validation_message,
                        'parsed_json': parsed_json if is_valid else None,
                        'schema_available': response_schema is not None
                    },
                    'success': True
                }

                results.append(result)

            except Exception as e:
                print(f"❌ Inference error: {e}")
                if "CUDA out of memory" in str(e):
                    print(f"💡 Try reducing max_tiles from {gpu_config['max_tiles']} to {gpu_config['max_tiles']//2}")
                continue

        except Exception as e:
            print(f"❌ Error processing prompt {i}: {e}")
            results.append({
                'prompt_id': f"prompt_{i}",
                'success': False,
                'error': str(e)
            })

    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    gpu_name_clean = torch.cuda.get_device_name(0).replace(' ', '_').replace('/', '_') if torch.cuda.is_available() else 'CPU'
    results_file = f'internvl3_universal_{gpu_name_clean}_{timestamp}.json'

    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    # Summary
    successful = [r for r in results if r.get('success', False)]

    print(f"\n📊 UNIVERSAL INFERENCE SUMMARY:")
    print("=" * 60)
    print(f"🤖 Model: InternVL3-8B (Universal)")
    print(f"💾 GPU: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'CPU'}")
    print(f"📊 VRAM: {gpu_config['total_vram']:.1f}GB")
    print(f"🔢 Precision: {gpu_config['recommended_precision']}")
    print(f"🖼️ Max images: {gpu_config['max_images']}")
    print(f"🔲 Max tiles: {gpu_config['max_tiles']}")
    print(f"📊 Processed: {len(results)} prompts")
    print(f"✅ Successful: {len(successful)}")

    if successful:
        avg_time = sum(r['inference_time'] for r in successful) / len(successful)
        total_urls = sum(r['urls_removed'] for r in successful)
        total_saved = sum(r['chars_saved'] for r in successful)
        avg_length = sum(r['generated_length'] for r in successful) / len(successful)
        avg_patches = sum(r['total_patches'] for r in successful) / len(successful)

        # Schema validation statistics
        valid_json_count = sum(1 for r in successful if r['schema_validation']['is_valid_json'])
        schema_compliance_rate = (valid_json_count / len(successful)) * 100

        print(f"⏱️ Average time: {avg_time:.2f}s")
        print(f"🔗 Total URLs removed: {total_urls:,}")
        print(f"💾 Total chars saved: {total_saved:,}")
        print(f"📝 Average response: {avg_length:,.0f} chars")
        print(f"🖼️ Average patches: {avg_patches:.1f}")
        print(f"📋 Schema compliance: {valid_json_count}/{len(successful)} ({schema_compliance_rate:.1f}%)")

        # GPU efficiency
        if torch.cuda.is_available():
            final_memory = torch.cuda.memory_reserved() / 1024**3
            memory_efficiency = (final_memory / gpu_config['total_vram']) * 100
            print(f"📊 GPU efficiency: {final_memory:.1f}GB ({memory_efficiency:.1f}% of {gpu_config['total_vram']:.1f}GB)")

    print(f"\n💾 Results saved to: {results_file}")
    print("🎉 Universal InternVL3-8B inference completed!")

if __name__ == "__main__":
    main()
