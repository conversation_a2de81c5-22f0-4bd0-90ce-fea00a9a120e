#!/usr/bin/env python3
"""
InternVL3 Inference using vLLM for final_prompt.jsonl
Downloads images from URLs and processes with InternVL3 model via vLLM.
Saves baseline.json and generated.json separately.

NOTE: This script requires vLLM to be installed (`pip install vllm`).
"""

import json
import time
import requests
import os
import re
from PIL import Image
from io import BytesIO
from vllm import LLM, SamplingParams
import logging
import warnings

warnings.filterwarnings("ignore")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_image_from_url(image_url: str) -> Image.Image | None:
    """Load a PIL image from a URL."""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(image_url, headers=headers, timeout=30)
        response.raise_for_status()
        image = Image.open(BytesIO(response.content)).convert('RGB')
        return image
    except Exception as e:
        logger.error(f"Failed to load image from {image_url}: {str(e)}")
        return None

def remove_urls_from_text(text: str):
    """Remove URLs from text to reduce token count"""
    url_patterns = [
        r'https?://[^\s<>"-]+',
        r'www\.[^\s<>"-]+',
        r'ftp://[^\s<>"-]+',
        r'[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:/[^\s<>"-]*)?',
    ]

    cleaned_text = text
    urls_removed = 0

    for pattern in url_patterns:
        matches = re.findall(pattern, cleaned_text)
        urls_removed += len(matches)
        cleaned_text = re.sub(pattern, '', cleaned_text)

    # Clean up whitespace
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
    cleaned_text = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_text)
    cleaned_text = cleaned_text.strip()

    return cleaned_text, urls_removed

def load_prompts_from_jsonl(file_path: str):
    """Load prompts from JSONL file with proper parsing for multi-line JSON objects."""
    print(f"📂 Loading prompts from {file_path}...")
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        prompts = []
        decoder = json.JSONDecoder()
        idx = 0
        # We strip leading whitespace from the file content
        content = content.strip()
        while idx < len(content):
            try:
                # raw_decode finds the next JSON object and its end position
                obj, end = decoder.raw_decode(content[idx:])
                prompts.append(obj)
                # Move index past the decoded object and any whitespace
                idx += end
                while idx < len(content) and content[idx].isspace():
                    idx += 1
            except json.JSONDecodeError:
                logger.error("Failed to decode JSON object, stopping parse.")
                break # Stop if we can't decode further

        print(f"✅ Loaded {len(prompts)} prompts")
        return prompts
    except Exception as e:
        print(f"❌ Error loading prompts: {str(e)}")
        return []

def initialize_vllm_internvl3():
    """Initialize InternVL3-8B model using vLLM."""
    print("🤖 Initializing InternVL3-8B model with vLLM...")
    try:
        # For multi-GPU, vLLM handles it with `tensor_parallel_size`
        # We can set it to the number of available GPUs.
        import torch
        gpu_count = torch.cuda.device_count()
        
        llm = LLM(
            model="OpenGVLab/InternVL3-8B",
            trust_remote_code=True,
            tensor_parallel_size=gpu_count if gpu_count > 0 else 1,
            enforce_eager=True, # Required for multi-modal models
            download_dir=os.environ.get("HUGGINGFACE_HUB_CACHE") # Optional: for caching
        )
        print("✅ vLLM InternVL3-8B model loaded successfully")
        return llm
    except Exception as e:
        print(f"❌ Failed to load model with vLLM: {str(e)}")
        print("💡 Troubleshooting:")
        print("  1. Ensure vLLM is installed (`pip install vllm`).")
        print("  2. Check CUDA and GPU driver compatibility with vLLM.")
        print("  3. Make sure you have enough GPU memory.")
        return None

def process_images_for_vllm(image_urls: list, max_images: int = 4):
    """Download images and prepare them for vLLM."""
    processed_images = []
    print(f"🖼️ Processing {len(image_urls)} image URLs (max {max_images})...")
    for i, url in enumerate(image_urls[:max_images]):
        print(f"  📥 Processing image {i+1}: {url}")
        image = load_image_from_url(url)
        if image:
            processed_images.append(image)
            print(f"    ✅ Image downloaded successfully.")
        else:
            print(f"    ❌ Failed to process")
    print(f"✅ Successfully processed {len(processed_images)} images")
    return processed_images

def run_vllm_inference(llm: LLM, prompt: str, images: list):
    """Run inference with vLLM and InternVL3."""
    try:
        # Construct the prompt with image placeholders
        if not images:
            final_prompt = prompt + " \n VERY IMPORTANT: Do not include the JSON schema in your response. Just return the JSON object in the format of the JSON schema."
        elif len(images) == 1:
            final_prompt = f"<image>\n{prompt} \n VERY IMPORTANT: Do not include the JSON schema in your response. Just return the JSON object in the format of the JSON schema."
        else:
            image_prefix = "".join([f"Image-{j+1}: <image>\n" for j in range(len(images))])
            final_prompt = f"{image_prefix}{prompt} \n VERY IMPORTANT: Do not include the JSON schema in your response. Just return the JSON object in the format of the JSON schema."

        print(f"📝 Final prompt length: {len(final_prompt):,} chars")

        # Setup sampling parameters
        sampling_params = SamplingParams(
            max_tokens=8192, # vLLM can handle large context
            temperature=0.3,
            top_p=0.9,
            repetition_penalty=1.05,
        )

        # Prepare multi-modal data for vLLM
        multi_modal_data = {"image": images} if images else None

        print("🚀 Running vLLM inference...")
        
        # vLLM generate method
        outputs = llm.generate(
            prompts=[final_prompt],
            sampling_params=sampling_params,
            multi_modal_data=multi_modal_data
        )

        # Extract the response text
        response = outputs[0].outputs[0].text
        return response

    except Exception as e:
        logger.error(f"vLLM inference failed: {str(e)}")
        return None

def clean_json_response(response_text: str) -> str:
    """Clean the generated response to extract valid JSON format."""
    if not response_text:
        return ""
    # Remove markdown code blocks
    response_text = re.sub(r'```json\s*', '', response_text)
    response_text = re.sub(r'```\s*$', '', response_text)
    response_text = re.sub(r'^```\s*', '', response_text)
    # Find JSON content between braces
    start_idx = response_text.find('{')
    if start_idx == -1:
        return response_text.strip()
    # Count braces to find the end of JSON
    brace_count = 0
    end_idx = start_idx
    for i, char in enumerate(response_text[start_idx:], start_idx):
        if char == '{':
            brace_count += 1
        elif char == '}':
            brace_count -= 1
            if brace_count == 0:
                end_idx = i + 1
                break
    json_content = response_text[start_idx:end_idx].strip()
    try:
        json.loads(json_content)
        return json_content
    except json.JSONDecodeError:
        return json_content

def main():
    """Main inference function"""
    print("🚀 InternVL3 vLLM Inference")
    print("=" * 50)

    # Load prompts
    prompts = load_prompts_from_jsonl('final_prompt.jsonl')
    if not prompts:
        print("❌ No prompts loaded")
        return

    # Initialize model
    llm = initialize_vllm_internvl3()
    if not llm:
        print("❌ vLLM Model initialization failed")
        return

    actual_responses = []
    generated_responses = []
    max_prompts = min(len(prompts), 2)

    print(f"\n🔄 Processing {max_prompts} prompts...")
    print("=" * 50)

    for i, prompt_data in enumerate(prompts[:max_prompts], 1):
        print(f"\n{'='*15} PROMPT {i}/{max_prompts} {'='*15}")
        try:
            prompt_text = prompt_data.get('prompt', '')
            expected_response = prompt_data.get('response', '')
            image_urls = prompt_data.get('images', [])

            print(f"📋 Data extracted: Prompt: {len(prompt_text):,} chars, Images: {len(image_urls)}")

            cleaned_prompt, _ = remove_urls_from_text(prompt_text)
            
            # Download images
            images = process_images_for_vllm(image_urls, max_images=6)
            if not images and image_urls:
                print("❌ No valid images, skipping prompt")
                continue

            start_time = time.time()
            generated_response = run_vllm_inference(llm, cleaned_prompt, images)
            inference_time = time.time() - start_time

            if generated_response:
                cleaned_response = clean_json_response(generated_response)
                print(f"✅ Inference completed in {inference_time:.2f}s")
                print(f"📝 Generated: {len(cleaned_response):,} chars")
                print(f"📄 Preview: {cleaned_response[:200]}...")
                actual_responses.append(expected_response)
                generated_responses.append(cleaned_response)
            else:
                print("❌ No response generated")
                actual_responses.append(expected_response)
                generated_responses.append("")

        except Exception as e:
            print(f"❌ Error processing prompt {i}: {e}")
            actual_responses.append(prompt_data.get('response', ''))
            generated_responses.append("")

    # Save results
    with open('response.json', 'w', encoding='utf-8') as f:
        json.dump(actual_responses, f, indent=2, ensure_ascii=False)
    with open('generate.json', 'w', encoding='utf-8') as f:
        json.dump(generated_responses, f, indent=2, ensure_ascii=False)

    successful_count = len([r for r in generated_responses if r.strip()])
    print(f"\n📊 SUMMARY:")
    print("=" * 50)
    print(f"📊 Processed: {len(generated_responses)} prompts")
    print(f"✅ Successful: {successful_count}")
    print(f"🤖 Model: InternVL3-8B with vLLM")
    print(f"📄 Average actual response: {sum(len(r) for r in actual_responses) / len(actual_responses):,.0f} chars" if actual_responses else "N/A")
    if successful_count > 0:
        print(f"📝 Average generated response: {sum(len(r) for r in generated_responses if r.strip()) / successful_count:,.0f} chars")
    print(f"\n💾 Results saved to response.json and generate.json")
    print("🎉 InternVL3 vLLM inference completed!")

if __name__ == "__main__":
    main()
